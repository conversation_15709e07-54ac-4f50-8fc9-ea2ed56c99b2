/*  Copyright 2019 devMiyax(<EMAIL>)

    This file is part of YabaSanshiro.

    <PERSON><PERSON>Sans<PERSON> is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    <PERSON><PERSON>Sans<PERSON> is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
*/

package org.uoyabause.android.phone

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.app.ProgressDialog
import android.app.UiModeManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.graphics.drawable.Icon
import android.net.Uri
import android.os.Build
import android.os.Build.VERSION_CODES
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider.NewInstanceFactory.Companion.instance
import androidx.lifecycle.lifecycleScope
import androidx.multidex.MultiDexApplication
import androidx.preference.PreferenceManager
import androidx.appcompat.widget.PopupMenu
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.widget.ImageButton
import android.widget.Filter
import com.bumptech.glide.Glide
import com.google.android.gms.analytics.HitBuilders.ScreenViewBuilder
import com.google.android.gms.analytics.Tracker
import com.google.android.material.navigation.NavigationView
import com.google.android.material.snackbar.Snackbar
import com.google.android.play.core.review.ReviewManager
import com.google.android.play.core.review.ReviewManagerFactory
import com.google.android.play.core.review.testing.FakeReviewManager
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import io.noties.markwon.Markwon
import io.reactivex.Observer
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.invoke
import kotlinx.coroutines.launch
import org.devmiyax.yabasanshiro.BuildConfig
import org.devmiyax.yabasanshiro.R
import org.devmiyax.yabasanshiro.StartupActivity
import org.uoyabause.android.*
import org.uoyabause.android.FileDialog.FileSelectedListener
import org.uoyabause.android.GameSelectPresenter.GameSelectPresenterListener
import org.uoyabause.android.AutoBackupManager
import org.uoyabause.android.YabauseStorage.Companion.dao
import org.uoyabause.android.backup.GameBackupManager
import org.uoyabause.android.tv.GameSelectFragment
import java.io.File
import java.util.*



class GameSelectFragmentPhone : Fragment(),
    GameItemAdapter.OnItemClickListener,
    NavigationView.OnNavigationItemSelectedListener,
    FileSelectedListener,
    GameSelectPresenterListener {
    lateinit var presenter: GameSelectPresenter
    private var observer: Observer<*>? = null
    private var drawerLayout: DrawerLayout? = null
    private var tracker: Tracker? = null
    private var firebaseAnalytics: FirebaseAnalytics? = null
    private var isFirstUpdate = true
    private var navigationView: NavigationView? = null
    private lateinit var rootView: View
    private lateinit var drawerToggle: ActionBarDrawerToggle
    private lateinit var recyclerView: RecyclerView
    private lateinit var searchView: SearchView
    private lateinit var sortButton: ImageButton
    private lateinit var gameAdapter: GameItemAdapter
    private lateinit var progressBar: View
    private lateinit var progressMessage: TextView
    private var isBackGroundComplete = false

    private var isBillingConnected = false
    private val viewModel by viewModels<BillingViewModel>()
    val connectionObserver = androidx.lifecycle.Observer<Boolean> { isConnecteed ->
        Log.d(BackupBackupItemFragment.TAG,"isConnected ${isConnecteed}")
        isBillingConnected = isConnecteed
    }



    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        instance = this
        presenter = GameSelectPresenter(this as Fragment, yabauseActivityLauncher,this)

        val remoteConfig = FirebaseRemoteConfig.getInstance()
        val configSettings = FirebaseRemoteConfigSettings.Builder()
            .setMinimumFetchIntervalInSeconds(3600)
            .build()
        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(R.xml.config)

        if(!remoteConfig.getBoolean("is_enable_subscription")){
            presenter.isOnSubscription = true
        }else {
            presenter.isOnSubscription = false
            viewModel.billingConnectionState.observe(this, connectionObserver)
            lifecycleScope.launchWhenStarted {
                viewModel.userCurrentSubscriptionFlow.collect { collectedSubscriptions ->
                    when {
                        collectedSubscriptions.hasPrepaidBasic == true -> {
                            Log.d(BackupBackupItemFragment.TAG, "hasPrepaidBasic")
                            if (presenter.isOnSubscription == false) {
                                presenter.isOnSubscription = true
                                presenter.syncBackup()
                            }

                        }
                        collectedSubscriptions.hasRenewableBasic == true -> {
                            Log.d(BackupBackupItemFragment.TAG, "hasRenewableBasic")
                            if (presenter.isOnSubscription == false) {
                                presenter.isOnSubscription = true
                                presenter.syncBackup()
                            }
                        }
                        else -> {
                            Log.d(BackupBackupItemFragment.TAG, "else")
                            presenter.isOnSubscription = false
                        }
                    }
                }
            }
        }

    }

    private var readRequestLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if ( result.resultCode == Activity.RESULT_OK) {
            if (result.data != null) {
                val uri = result.data!!.data
                if (uri != null) {
                    presenter.onSelectFile(uri)
                }
            }
        }
    }

    private fun selectGameFile(){
        val prefs = requireActivity().getSharedPreferences("private",
            MultiDexApplication.MODE_PRIVATE)
        val installCount = prefs.getInt("InstallCount", 3)
        if( installCount > 0){
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
            intent.addCategory(Intent.CATEGORY_OPENABLE)
            intent.type = "*/*"
            readRequestLauncher.launch(intent)
        }else {
            val message = resources.getString(org.devmiyax.yabasanshiro.R.string.or_place_file_to, YabauseStorage.storage.gamePath)
            val rtn = YabauseApplication.checkDonated(requireActivity(), message)
            if ( rtn == 0) {
                val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
                intent.addCategory(Intent.CATEGORY_OPENABLE)
                intent.type = "*/*"
                readRequestLauncher.launch(intent)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        rootView = inflater.inflate(org.devmiyax.yabasanshiro.R.layout.fragment_game_select_fragment_phone, container, false)
        progressBar = rootView.findViewById(org.devmiyax.yabasanshiro.R.id.llProgressBar)
        progressBar.visibility = View.GONE
        progressMessage = rootView.findViewById(org.devmiyax.yabasanshiro.R.id.pbText)

        // RecyclerViewの設定
        recyclerView = rootView.findViewById(org.devmiyax.yabasanshiro.R.id.recycler_view_games)
        recyclerView.layoutManager = LinearLayoutManager(context)

        // 検索バーの設定
        searchView = rootView.findViewById(org.devmiyax.yabasanshiro.R.id.search_view)

        // ソートボタンの設定
        sortButton = rootView.findViewById(org.devmiyax.yabasanshiro.R.id.sort_button)
        sortButton.setOnClickListener {
            showSortMenu(it)
        }

        val fab: View = rootView.findViewById(org.devmiyax.yabasanshiro.R.id.fab)
        if (Build.VERSION.SDK_INT >= VERSION_CODES.Q) {
            fab.setOnClickListener {
                selectGameFile()
            }
        } else {
            fab.visibility = View.GONE
        }



        if( adHeight != 0 ) {
            onAdViewIsShown(adHeight)
        }
        return rootView
    }



    /**
     * Fetches cloud-backed games that aren't downloaded locally and adds them to the game list
     */
    private suspend fun fetchCloudOnlyGames(): List<GameInfo> {
        // Check if user is signed in
        val auth = FirebaseAuth.getInstance()
        if (auth.currentUser == null) {
            return emptyList()
        }

        try {
            // Get backed up games
            val gameBackupManager = org.uoyabause.android.backup.GameBackupManager(requireContext())
            val backedUpGames = gameBackupManager.getBackedUpGames()

            if (backedUpGames.isEmpty()) {
                return emptyList()
            }

            // Get local games to filter out games that are already downloaded
            val localGames = YabauseStorage.dao.getAll()
            val localProductNumbers = localGames.map { it.product_number }

            // Filter out games that are already downloaded
            val cloudOnlyGames = backedUpGames.filter { backupGame ->
                !localProductNumbers.contains(backupGame.productNumber)
            }

            // Convert to GameInfo objects
            return cloudOnlyGames.map { backupGame ->
                CloudGameInfo(backupGame).toGameInfo()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching cloud-only games: ${e.message}")
            return emptyList()
        }
    }

    private fun showSortMenu(view: View) {
        val popup = PopupMenu(requireContext(), view)
        popup.menuInflater.inflate(R.menu.sort_menu, popup.menu)
        popup.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.sort_by_name -> {
                    gameAdapter.sortByName()
                    true
                }
                R.id.sort_by_date -> {
                    gameAdapter.sortByDate()
                    true
                }
                R.id.sort_by_recently_played -> {
                    gameAdapter.sortByRecentlyPlayed()
                    true
                }
                else -> false
            }
        }
        popup.show()
    }

    private var adHeight = 0
    fun onAdViewIsShown(height: Int) {
        try {
            val parentLayout = rootView.findViewById<DrawerLayout>(org.devmiyax.yabasanshiro.R.id.drawer_layout_game_select)
            val param = parentLayout.layoutParams as FrameLayout.LayoutParams
            param.bottomMargin = height + 4
            parentLayout.layoutParams = param
        } catch (e: Exception) {
            adHeight = height
        }
    }

    override fun onPrepareOptionsMenu(menu: Menu): Unit {
        val menuItem = menu.findItem(R.id.menu_auto_backupsync)
        menuItem.isEnabled = isBillingConnected // メニューアイテムが選択可能かどうかを判定し、isEnabled プロパティに設定する
        return
    }

    suspend fun startSub(){
        if( viewModel.billingConnectionState.value == true) {
            val YEARLY_BASIC_PLANS_TAG = "yearly-basic"
            viewModel.productsForSaleFlows.collectLatest { it ->
                it.let {
                    viewModel.buy(
                        productDetails = it,
                        currentPurchases = null,
                        tag = YEARLY_BASIC_PLANS_TAG,
                        activity = requireActivity()
                    )
                }
            }
        }
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        drawerLayout!!.closeDrawers()
        when (item.itemId) {
            org.devmiyax.yabasanshiro.R.id.menu_auto_backupsync -> {
                firebaseAnalytics?.logEvent("game_select_fragment"){
                    param("event", "menu_auto_backupsync")
                }

                if( presenter.isOnSubscription ) {
                    val fragment = BackupBackupItemFragment.newInstance(1, presenter)
                    val transaction = requireActivity().supportFragmentManager.beginTransaction()
                    transaction.replace(org.devmiyax.yabasanshiro.R.id.ext_fragment, fragment)
                    transaction.addToBackStack(null)
                    transaction.commit()
                }else{

                    AlertDialog.Builder(requireActivity())
                        .setTitle("Subscribe Auto backup")
                        .setMessage("fee: $1/year \n *Automatically backup data to cloud \n *Rollback \n *Share backup data between devices")
                        .setPositiveButton(R.string.yes){ _, _->
                            lifecycleScope.launch {
                                startSub()
                            }
                        }.setNegativeButton(R.string.no){ _, _->

                        }
                        .show()

                }

            }
            org.devmiyax.yabasanshiro.R.id.menu_item_setting -> {

                firebaseAnalytics?.logEvent("game_select_fragment"){
                    param("event", "menu_item_setting")
                }

                val intent = Intent(activity, SettingsActivity::class.java)
                settingActivityLauncher.launch(intent)
            }
            org.devmiyax.yabasanshiro.R.id.menu_item_load_game -> {

                firebaseAnalytics?.logEvent("game_select_fragment"){
                    param("event", "menu_item_load_game")
                }

                if (Build.VERSION.SDK_INT >= VERSION_CODES.Q) {
                    selectGameFile()
                } else {
                    val sharedPref =
                        PreferenceManager.getDefaultSharedPreferences(this.requireActivity())
                    val lastDir =
                        sharedPref.getString("pref_last_dir", YabauseStorage.storage.gamePath)
                    val fd =
                        FileDialog(requireActivity(), lastDir)
                    fd.addFileListener(this)
                    fd.showDialog()
                }
            }
            org.devmiyax.yabasanshiro.R.id.menu_item_update_game_db -> {

                firebaseAnalytics?.logEvent("game_select_fragment"){
                    param("event", "menu_item_update_game_db")
                }

                if (checkStoragePermission() == 0) {
                    updateGameList(3)
                }
            }
            org.devmiyax.yabasanshiro.R.id.menu_item_login -> if (item.title == getString(org.devmiyax.yabasanshiro.R.string.sign_out)) {

                firebaseAnalytics?.logEvent("game_select_fragment"){
                    param("event", "menu_item_login")
                }

                presenter.signOut()
                item.setTitle(org.devmiyax.yabasanshiro.R.string.sign_in)
            } else {
                presenter.signIn(signInActivityLauncher)
            }
            org.devmiyax.yabasanshiro.R.id.menu_privacy_policy -> {

                firebaseAnalytics?.logEvent("game_select_fragment"){
                    param("event", "menu_privacy_policy")
                }

                val uri =
                    Uri.parse("https://www.yabasanshiro.com/privacy")
                val i = Intent(Intent.ACTION_VIEW, uri)
                startActivity(i)
            }

        }
        return false
    }

    private fun checkStoragePermission(): Int {
        if ( Build.VERSION.SDK_INT < VERSION_CODES.Q ) { // Verify that all required contact permissions have been granted.
            if (ActivityCompat.checkSelfPermission(
                    requireActivity().applicationContext,
                    Manifest.permission.READ_EXTERNAL_STORAGE
                )
                != PackageManager.PERMISSION_GRANTED ||
                ActivityCompat.checkSelfPermission(
                    requireActivity().applicationContext,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
                != PackageManager.PERMISSION_GRANTED
            ) { // Contacts permissions have not been granted.
                Log.i(
                    TAG,
                    "Storage permissions has NOT been granted. Requesting permissions."
                )
                requestStoragePermission.launch(PERMISSIONS_STORAGE)
                return -1
            }
        }
        return 0
    }

    private val requestStoragePermission = registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
        result.entries.forEach{
            if(!it.value){
                showRestartMessage()
                return@registerForActivityResult
            }
        }
        updateGameList(0)
    }

    private fun showSnackBar(id: Int) {
        Snackbar
            .make(rootView.rootView, getString(id), Snackbar.LENGTH_SHORT)
            .show()
    }

    private fun updateRecent() {
        // 最近プレイしたゲームのリストを取得して表示を更新
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val recentList = YabauseStorage.dao.getRecentGames()

                // Get cloud-only games
                val cloudOnlyGames = fetchCloudOnlyGames()

                launch(Dispatchers.Main) {
                    // 全ゲームリストを再取得
                    val localGames = YabauseStorage.dao.getAllSortedByTitle()

                    // Create a new mutable list with the correct type
                    val combinedGames: MutableList<GameInfo?> = mutableListOf()

                    // Add local games
                    combinedGames.addAll(localGames)

                    // Add cloud-only games if there are any
                    if (cloudOnlyGames.isNotEmpty()) {
                        combinedGames.addAll(cloudOnlyGames)
                    }

                    // Assign to allGames
                    allGames = combinedGames

                    gameAdapter = GameItemAdapter(allGames)
                    gameAdapter.setOnItemClickListener(this@GameSelectFragmentPhone)
                    recyclerView.adapter = gameAdapter

                    // 現在のソート順を維持
                    gameAdapter.sortByRecentlyPlayed()
                }
            } catch (e: Exception) {
                Log.d(TAG, e.localizedMessage ?: "Error updating recent games")
            }
        }
    }

    private var adActivityLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        updateRecent()
    }

    private var settingActivityLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == GameSelectFragment.GAMELIST_NEED_TO_UPDATED) {
            if (checkStoragePermission() == 0) {

                firebaseAnalytics?.logEvent("game_select_fragment"){
                    param("event", "GAMELIST_NEED_TO_UPDATED")
                }

                updateGameList(3)
            }
        }else if (result.resultCode == GameSelectFragment.GAMELIST_NEED_TO_RESTART) {

            firebaseAnalytics?.logEvent("game_select_fragment"){
                param("event", "GAMELIST_NEED_TO_RESTART")
            }

            val intent = Intent(activity, StartupActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
            activity?.finish()
        }
    }

    private var signInActivityLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->

        firebaseAnalytics?.logEvent("game_select_fragment"){
            param("event", "onSignIn")
        }

        presenter.onSignIn(result.resultCode, result.data)
        if (presenter.currentUserName != null) {
            val m = navigationView!!.menu
            val miLogin = m.findItem(org.devmiyax.yabasanshiro.R.id.menu_item_login)
            miLogin.setTitle(org.devmiyax.yabasanshiro.R.string.sign_out)
        }
    }

    private var yabauseActivityLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {

        val playtime = it.data?.getLongExtra("playTime",0) ?: 0L

        Log.d(TAG, "Play time is ${playtime}")

        firebaseAnalytics?.logEvent("game_select_fragment"){
            param("event", "On Game Finished")
            param("playTime",playtime)
        }
        val prefs = requireActivity().getSharedPreferences(
            "private",
            Context.MODE_PRIVATE
        )
        val hasDonated = prefs?.getBoolean("donated", false)

        if (BuildConfig.BUILD_TYPE != "pro" && hasDonated == false ) {

                val rn = Math.random()
                if (rn <= 0.3) {
                    val uiModeManager =
                        activity?.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
                    if (uiModeManager.currentModeType != Configuration.UI_MODE_TYPE_TELEVISION) {
                        val intent = Intent(
                            activity,
                            AdActivity::class.java
                        )
                        adActivityLauncher.launch(intent)
                        // }
                    } else {
                        val intent =
                            Intent(activity, AdActivity::class.java)
                        adActivityLauncher.launch(intent)
                    }
                } else if (rn <= 0.6) {
                    val intent =
                        Intent(activity, AdActivity::class.java)
                    adActivityLauncher.launch(intent)
                } else {

                    val lastReviewDateTime = prefs.getInt("last_review_date_time",0)
                    val unixTime = System.currentTimeMillis() / 1000L

                    // ３ヶ月に一度レビューしてもらう
                    if( (unixTime - lastReviewDateTime) > 60*60*24*30 ) {

                        // 5分以上遊んだ？
                        if( playtime < 5*60 ) return@registerForActivityResult

                        var manager : ReviewManager? = null
                        if( BuildConfig.DEBUG ){
                            manager = FakeReviewManager(requireContext())
                        }else{
                            val editor = prefs.edit()
                            editor.putInt("last_review_date_time",lastReviewDateTime)
                            editor.commit()
                            manager = ReviewManagerFactory.create(requireContext())
                        }
                        val request = manager.requestReviewFlow()
                        request.addOnCompleteListener { task ->
                            if (task.isSuccessful) {
                                // We got the ReviewInfo object
                                val reviewInfo = task.result
                                val flow = manager?.launchReviewFlow(requireActivity(), reviewInfo)
                                flow?.addOnCompleteListener { _ ->

                                }
                            } else {
                                task.getException()?.message?.let {
                                        it1 -> Log.d( TAG, it1)
                                }
                            }
                        }

                    }else{
                        val intent =
                            Intent(activity, AdActivity::class.java)
                        adActivityLauncher.launch(intent)
                    }
                }

            updateRecent()

        } else {

            val rn = Math.random()
            val lastReviewDateTime = prefs.getInt("last_review_date_time",0)
            val unixTime = System.currentTimeMillis() / 1000L

            // ３ヶ月に一度レビューしてもらう
            if( rn < 0.3 && (unixTime - lastReviewDateTime) > 60*60*24*30 ){

                // 5分以上遊んだ？
                if( playtime < 5*60 ) return@registerForActivityResult

                var manager : ReviewManager? = null
                if( BuildConfig.DEBUG ){
                    manager = FakeReviewManager(requireContext())
                }else{
                    val editor = prefs.edit()
                    editor.putInt("last_review_date_time",lastReviewDateTime)
                    editor.commit()
                    manager = ReviewManagerFactory.create(requireContext())
                }
                val request = manager.requestReviewFlow()
                request.addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        // We got the ReviewInfo object
                        val reviewInfo = task.result
                        val flow = manager.launchReviewFlow(requireActivity(), reviewInfo)
                        flow.addOnCompleteListener { _ ->

                        }
                    } else {
                        task.getException()?.message?.let {
                                it1 -> Log.d( TAG, it1)
                        }
                    }
                }
            }
            updateRecent()
        }
    }

    override fun fileSelected(file: File?) {

        firebaseAnalytics?.logEvent("game_select_fragment"){
            param("event", "fileSelected")
        }

        if( file != null ) {
            presenter.fileSelected(file)
        }
    }

    fun showDialog(message: String?) {
        if (message != null) {
            progressMessage.text = message
        } else {
            progressMessage.text = getString(org.devmiyax.yabasanshiro.R.string.updating)
        }
        progressBar.visibility = VISIBLE
    }

    fun updateDialogString(msg: String) {
        progressMessage.text = msg
    }

    fun dismissDialog() {
        progressBar.visibility = View.GONE
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view,savedInstanceState)
        val activity = requireActivity() as AppCompatActivity
        firebaseAnalytics = FirebaseAnalytics.getInstance(activity)
        val application = activity.application as YabauseApplication
        tracker = application.defaultTracker
        val toolbar =
            rootView.findViewById<View>(org.devmiyax.yabasanshiro.R.id.toolbar) as Toolbar
        toolbar.setLogo(org.devmiyax.yabasanshiro.R.mipmap.ic_launcher)
        toolbar.title = getString(org.devmiyax.yabasanshiro.R.string.app_name)
        toolbar.subtitle = getVersionName(activity)
        activity.setSupportActionBar(toolbar)

        drawerLayout =
            rootView.findViewById<View>(org.devmiyax.yabasanshiro.R.id.drawer_layout_game_select) as DrawerLayout




        drawerToggle = object : ActionBarDrawerToggle(
            getActivity(), /* host Activity */
            drawerLayout, /* DrawerLayout object */
            org.devmiyax.yabasanshiro.R.string.drawer_open, /* "open drawer" description */
            org.devmiyax.yabasanshiro.R.string.drawer_close /* "close drawer" description */
        ) {


            override fun onDrawerOpened(drawerView: View) {
                super.onDrawerOpened(drawerView)
                // activity.getSupportActionBar().setTitle("bbb");

                val tx = rootView.findViewById<TextView?>(org.devmiyax.yabasanshiro.R.id.menu_title)
                val uname = presenter.currentUserName

                if( tx?.text != uname ) {
                    if (tx != null && uname != null) {
                        tx.text = uname
                    } else {
                        tx.text = ""
                    }
                    val iv =
                        rootView.findViewById<ImageView?>(org.devmiyax.yabasanshiro.R.id.navi_header_image)
                    val uri = presenter.currentUserPhoto
                    if (iv != null && uri != null) {
                        Glide.with(drawerView.context)
                            .load(uri)
                            .into(iv)
                    } else {
                        iv.setImageResource(org.devmiyax.yabasanshiro.R.mipmap.ic_launcher)
                    }
                }
            }

        }
        // Set the drawer toggle as the DrawerListener
        drawerLayout!!.addDrawerListener(drawerToggle)
        activity.supportActionBar!!.setDisplayHomeAsUpEnabled(true)
        activity.supportActionBar!!.setHomeButtonEnabled(true)
        drawerToggle.syncState()
        navigationView =
            rootView.findViewById<View>(org.devmiyax.yabasanshiro.R.id.nav_view) as NavigationView
        if (navigationView != null) {
            navigationView!!.setNavigationItemSelectedListener(this)

            val headerView = navigationView!!.getHeaderView(0)
            val drawerView = headerView!!.findViewById<ImageView>(org.devmiyax.yabasanshiro.R.id.navi_header_image)
            val uri = presenter.currentUserPhoto

            if( uri != null ) {
                val icon: Icon = Icon.createWithResource(
                    requireContext(),
                    org.devmiyax.yabasanshiro.R.mipmap.ic_launcher
                )
                val drawable = icon.loadDrawable(context)
                if (drawable != null) {
                    val width = drawable.intrinsicWidth
                    val height = drawable.intrinsicHeight

                    Glide.with(requireActivity())
                        .load(uri)
                        .override(width, height) // 幅500px、高さ500pxにリサイズ
                        .centerCrop() // センタークロップ
                        .into(drawerView)
                }
            }else{
                drawerView.setImageResource(org.devmiyax.yabasanshiro.R.mipmap.ic_launcher)
            }

            val tx = headerView.findViewById<TextView?>(org.devmiyax.yabasanshiro.R.id.menu_title)
            val uname = presenter.currentUserName
            if (tx != null && uname != null) {
                tx.text = uname
            } else {
                tx.text = ""
            }

        }



        if (presenter.currentUserName != null) {
            val m = navigationView!!.menu
            val miLogin = m.findItem(org.devmiyax.yabasanshiro.R.id.menu_item_login)
            miLogin.setTitle(org.devmiyax.yabasanshiro.R.string.sign_out)
        } else {
            val m = navigationView!!.menu
            val miLogin = m.findItem(org.devmiyax.yabasanshiro.R.id.menu_item_login)
            miLogin.setTitle(org.devmiyax.yabasanshiro.R.string.sign_in)
        }
        if (checkStoragePermission() == 0) {
            updateGameList(0)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        drawerToggle.onConfigurationChanged(newConfig)

        // 画面の向きに応じてRecyclerViewのレイアウトを変更
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            recyclerView.layoutManager = GridLayoutManager(context, 2)
        } else {
            recyclerView.layoutManager = LinearLayoutManager(context)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Pass the event to ActionBarDrawerToggle, if it returns
// true, then it has handled the app icon touch event
        return if (drawerToggle.onOptionsItemSelected(item)) {
            true
        } else super.onOptionsItemSelected(
            item
        )
    }

    private fun updateGameList( level: Int ) {
        if (observer != null) return
        isBackGroundComplete = false
        val tmpObserver = object : Observer<String> {
            // GithubRepositoryApiCompleteEventEntity eventResult = new GithubRepositoryApiCompleteEventEntity();
            override fun onSubscribe(d: Disposable) {
                showDialog(null)
            }

            override fun onNext(response: String) {
                updateDialogString("${getString(org.devmiyax.yabasanshiro.R.string.updating)} $response")
            }

            override fun onError(e: Throwable) {

                firebaseAnalytics?.logEvent("game_select_fragment"){
                    param("event", "updateGameList onError")
                }

                observer = null
                dismissDialog()
                presenter.syncBackup()
            }

            override fun onComplete() {

                firebaseAnalytics?.logEvent("game_select_fragment"){
                    param("event", "updateGameList onComplete")
                }

                if (!isFront) {
                    observer = null
                    dismissDialog()
                    isBackGroundComplete = true
                    return
                }

                loadRows()

                dismissDialog()
                if (isFirstUpdate) {
                    isFirstUpdate = false
                    if (<EMAIL>().intent!!.getBooleanExtra(
                            "showPin",
                            false
                        )
                    ) {
                        ShowPinInFragment.newInstance().show(
                            childFragmentManager,
                            "sample"
                        )
                    } else {
                        presenter.checkSignIn(signInActivityLauncher)
                    }
                }

                observer = null
                presenter.syncBackup()


            }
        }
        observer = tmpObserver
        presenter.updateGameList(level, tmpObserver)
    }

    private fun showRestartMessage() { // need_to_accept
        val viewMessageParent = rootView.findViewById<ScrollView?>(org.devmiyax.yabasanshiro.R.id.empty_message_parent)
        val viewMessage = rootView.findViewById<TextView?>(org.devmiyax.yabasanshiro.R.id.empty_message)
        recyclerView.visibility = View.GONE
        viewMessageParent?.visibility = VISIBLE

        val welcomeMessage = resources.getString(org.devmiyax.yabasanshiro.R.string.need_to_accept)
        viewMessage.text = welcomeMessage
    }

    private var allGames: MutableList<GameInfo?>? = null

    private fun loadRows() {
        Log.d("GameSelect", "loadRows")

        GlobalScope.launch(Dispatchers.IO) {
            // ゲーム数を確認
            var dataCount = 0
            try {
                val glist: List<GameInfo> = YabauseStorage.dao.getAll()
                dataCount = YabauseStorage.dao.getRowCount()
                if (glist.size != dataCount) {
                    Log.d(TAG, "dataCount is not match")
                }
            } catch (e: Exception) {
                Log.d(TAG, e.localizedMessage!!)
            }

            // Get cloud-only games
            val cloudOnlyGames = fetchCloudOnlyGames()
            val totalGameCount = dataCount + cloudOnlyGames.size

            if (totalGameCount == 0) {
                // ゲームがない場合はウェルカムメッセージを表示
                launch(Dispatchers.Main) {
                    val viewMessageParent = rootView.findViewById<ScrollView?>(org.devmiyax.yabasanshiro.R.id.empty_message_parent)
                    val viewMessage = rootView.findViewById<TextView?>(org.devmiyax.yabasanshiro.R.id.empty_message)
                    recyclerView.visibility = View.GONE
                    viewMessageParent!!.visibility = VISIBLE

                    val markwon = Markwon.create(<EMAIL> as Context)

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                        val welcomeMessage = resources.getString(
                            org.devmiyax.yabasanshiro.R.string.welcome_11,
                            YabauseStorage.storage.gamePath,
                            "",
                        )
                        markwon.setMarkdown(viewMessage, welcomeMessage)
                    }
                    else if (Build.VERSION.SDK_INT >= VERSION_CODES.Q) {
                        val packageName = requireActivity().packageName
                        val welcomeMessage = resources.getString(
                            org.devmiyax.yabasanshiro.R.string.welcome_11,
                            "Android/data/$packageName/files/yabause/games",
                            "Android/data/$packageName/files",
                        )
                        markwon.setMarkdown(viewMessage, welcomeMessage)
                    } else {
                        val welcomeMessage = resources.getString(
                            org.devmiyax.yabasanshiro.R.string.welcome,
                            YabauseStorage.storage.gamePath
                        )
                        markwon.setMarkdown(viewMessage, welcomeMessage)
                    }
                }
                return@launch
            }

            // ゲームがある場合はリストを表示
            launch(Dispatchers.Main) {
                val viewMessageParent = rootView.findViewById<ScrollView?>(org.devmiyax.yabasanshiro.R.id.empty_message_parent)
                viewMessageParent?.visibility = View.GONE
                recyclerView.visibility = VISIBLE

                // すべてのゲームを取得
                GlobalScope.launch(Dispatchers.IO) {
                    try {
                        // Get local games
                        val localGames = YabauseStorage.dao.getAllSortedByTitle()

                        // Create a new mutable list with the correct type
                        val combinedGames: MutableList<GameInfo?> = mutableListOf()

                        // Add local games
                        combinedGames.addAll(localGames)

                        // Add cloud-only games if there are any
                        if (cloudOnlyGames.isNotEmpty()) {
                            combinedGames.addAll(cloudOnlyGames)
                        }

                        // Assign to allGames
                        allGames = combinedGames

                        launch(Dispatchers.Main) {
                            gameAdapter = GameItemAdapter(allGames)
                            gameAdapter.setOnItemClickListener(this@GameSelectFragmentPhone)
                            recyclerView.adapter = gameAdapter

                            // 検索バーのリスナーを設定
                            searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
                                override fun onQueryTextSubmit(query: String?): Boolean {
                                    return false
                                }

                                override fun onQueryTextChange(newText: String?): Boolean {
                                    gameAdapter.filter.filter(newText)
                                    return true
                                }
                            })

                            // デフォルトでは名前順にソート
                            gameAdapter.sortByName()
                        }
                    } catch (e: Exception) {
                        Log.d(TAG, "${e.localizedMessage}")
                    }
                }
            }
        }
    }

    override fun onItemClick(position: Int, item: GameInfo?, v: View?) {
        if (item != null) {
            if (item.isCloudOnly && item.cloudBackupInfo != null) {
                // Handle cloud-only game click - download it first
                downloadCloudGame(item.cloudBackupInfo!!)
            } else {
                // Normal game click - start the game
                presenter.startGame(item, yabauseActivityLauncher)
            }
        }
    }

    /**
     * Downloads a cloud-backed game
     */
    private fun downloadCloudGame(backupGameInfo: org.uoyabause.android.backup.GameBackupManager.BackupGameInfo) {
        // Show progress dialog
        val progressDialog = ProgressDialog(requireContext())
        progressDialog.setMessage("Downloading game...")
        progressDialog.setCancelable(false)
        progressDialog.show()

        // Get game backup manager
        val gameBackupManager = org.uoyabause.android.backup.GameBackupManager(requireContext())

        // Launch coroutine to restore game
        CoroutineScope(Dispatchers.Main).launch {
            try {
                val result = gameBackupManager.restoreGame(backupGameInfo)

                // Dismiss progress dialog
                progressDialog.dismiss()

                // Show result
                if (result.success) {
                    Toast.makeText(
                        requireContext(),
                        getString(org.devmiyax.yabasanshiro.R.string.restore_success),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Refresh game list
                    updateGameList(YabauseStorage.REFRESH_LEVEL_REBUILD)
                } else {
                    Toast.makeText(
                        requireContext(),
                        "${getString(org.devmiyax.yabasanshiro.R.string.restore_failed)}: ${result.message}",
                        Toast.LENGTH_LONG
                    ).show()
                }
            } catch (e: Exception) {
                // Dismiss progress dialog
                progressDialog.dismiss()

                Toast.makeText(
                    requireContext(),
                    "${getString(org.devmiyax.yabasanshiro.R.string.restore_failed)}: ${e.message}",
                    Toast.LENGTH_LONG
                ).show()
            }
        }
    }

    override fun onGameRemoved(item: GameInfo?) {
        firebaseAnalytics?.logEvent("game_select_fragment"){
            param("event", "onGameRemoved")
        }

        if (item == null) return

        // アダプターから削除
        gameAdapter.removeItem(item.id)
    }

    override fun onResume() {
        super.onResume()
        if (tracker != null) { // mTracker.setScreenName(TAG);
            tracker!!.send(ScreenViewBuilder().build())
        }
        if (presenter.currentUserName != null) {
            val m = navigationView!!.menu
            val miLogin = m.findItem(org.devmiyax.yabasanshiro.R.id.menu_item_login)
            miLogin.setTitle(org.devmiyax.yabasanshiro.R.string.sign_out)
        } else {
            val m = navigationView!!.menu
            val miLogin = m.findItem(org.devmiyax.yabasanshiro.R.id.menu_item_login)
            miLogin.setTitle(org.devmiyax.yabasanshiro.R.string.sign_in)
        }
        isFront = true
        if (isBackGroundComplete) {
            updateGameList(0)
        }
        presenter.onResume()
    }

    var isFront = true

    override fun onPause() {
        isFront = false
        super.onPause()
        this.presenter.onPause()
    }



    override fun onDestroy() {
        System.gc()
        super.onDestroy()
    }

    override fun onShowMessage(string_id: Int) {
        showSnackBar(string_id)
    }

    override fun onShowDialog(message: String) {
        showDialog(message)
    }

    override fun onUpdateDialogMessage(message: String) {
        updateDialogString(message)
    }

    override fun onDismissDialog() {
        dismissDialog()
    }

    override fun onLoadRows() {
        loadRows()
    }


    override fun onStartSyncBackUp(){
    }

    override fun onFinishSyncBackUp(result: AutoBackupManager.SyncResult, message: String) {
        if( result == AutoBackupManager.SyncResult.SUCCESS ){
            Snackbar.make(rootView.rootView, message, Snackbar.LENGTH_LONG).show();
        }

        if( result == AutoBackupManager.SyncResult.FAIL ){
            val color = ContextCompat.getColor(requireContext(), org.devmiyax.yabasanshiro.R.color.design_default_color_error)
            val snackbar = Snackbar.make(rootView.rootView, message, Snackbar.LENGTH_LONG)
            snackbar.setTextColor( color )
            snackbar.show()
        }


    }

    override fun onSignOut() {

    }

    companion object {
        private const val TAG = "GameSelectFragmentPhone"
        private var instance: GameSelectFragmentPhone? = null

        @JvmField
        var myOnClickListener: View.OnClickListener? = null
        private val PERMISSIONS_STORAGE = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )

        fun newInstance(): GameSelectFragmentPhone {
            val fragment = GameSelectFragmentPhone()
            val args = Bundle()
            fragment.arguments = args
            return fragment
        }

        fun getInstance(): GameSelectFragmentPhone? {
            return instance
        }

        fun getVersionName(context: Context): String {
            val pm = context.packageManager
            var versionName = ""
            try {
                val packageInfo = pm.getPackageInfo(context.packageName, 0)
                versionName = packageInfo.versionName
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
            }
            return versionName
        }
    }
}
